"""Tests for persona team validation functionality."""

import pytest
from unittest.mock import Mo<PERSON>, <PERSON><PERSON>ock
from fastapi import HTTP<PERSON>x<PERSON>
from sqlalchemy.orm import Session

from onyx.utils.user_teams_validation import (
    validate_document_sets_belong_to_team,
    validate_tools_belong_to_team
)
from onyx.db.models import DocumentSet, Tool, DocumentSet__UserGroup, Tool__UserTeams


class TestDocumentSetTeamValidation:
    """Test validation of document sets belonging to teams."""
    
    def test_validate_document_sets_empty_list(self):
        """Test that empty document set list passes validation."""
        db_session = Mock(spec=Session)
        
        # Should not raise any exception
        validate_document_sets_belong_to_team(
            document_set_ids=[],
            user_teams=[1],
            db_session=db_session
        )
        
        # No database queries should be made
        db_session.query.assert_not_called()
    
    def test_validate_document_sets_public_persona_with_private_doc_sets(self):
        """Test that public personas cannot use private document sets."""
        db_session = Mock(spec=Session)
        
        # Mock private document set
        private_doc_set = Mock()
        private_doc_set.name = "Private Doc Set"
        private_doc_set.is_public = False
        
        db_session.query.return_value.filter.return_value.all.return_value = [private_doc_set]
        
        with pytest.raises(HTTPException) as exc_info:
            validate_document_sets_belong_to_team(
                document_set_ids=[1],
                user_teams=[],  # Public persona (no teams)
                db_session=db_session
            )
        
        assert exc_info.value.status_code == 400
        assert "Public assistants cannot use private document sets" in exc_info.value.detail
        assert "Private Doc Set" in exc_info.value.detail
    
    def test_validate_document_sets_public_persona_with_public_doc_sets(self):
        """Test that public personas can use public document sets."""
        db_session = Mock(spec=Session)
        
        # Mock that no private document sets are found
        db_session.query.return_value.filter.return_value.all.return_value = []
        
        # Should not raise any exception
        validate_document_sets_belong_to_team(
            document_set_ids=[1, 2],
            user_teams=[],  # Public persona (no teams)
            db_session=db_session
        )
    
    def test_validate_document_sets_private_persona_with_team_doc_sets(self):
        """Test that private personas can use document sets belonging to their team."""
        db_session = Mock(spec=Session)
        
        # Mock document set that belongs to the team
        doc_set = Mock()
        doc_set.id = 1
        doc_set.name = "Team Doc Set"
        doc_set.is_public = False
        
        # Mock team association
        team_association = Mock()
        
        # Setup query mocks
        query_mock = Mock()
        db_session.query.return_value = query_mock
        
        # First query for document sets
        query_mock.filter.return_value.all.return_value = [doc_set]
        
        # Second query for team association
        query_mock.filter.return_value.first.return_value = team_association
        
        # Should not raise any exception
        validate_document_sets_belong_to_team(
            document_set_ids=[1],
            user_teams=[1],
            db_session=db_session
        )
    
    def test_validate_document_sets_private_persona_without_team_access(self):
        """Test that private personas cannot use document sets not belonging to their team."""
        db_session = Mock(spec=Session)
        
        # Mock document set that doesn't belong to the team
        doc_set = Mock()
        doc_set.id = 1
        doc_set.name = "Other Team Doc Set"
        doc_set.is_public = False
        
        # Setup query mocks
        query_mock = Mock()
        db_session.query.return_value = query_mock
        
        # First query for document sets
        query_mock.filter.return_value.all.return_value = [doc_set]
        
        # Second query for team association (no association found)
        query_mock.filter.return_value.first.return_value = None
        
        with pytest.raises(HTTPException) as exc_info:
            validate_document_sets_belong_to_team(
                document_set_ids=[1],
                user_teams=[1],
                db_session=db_session
            )
        
        assert exc_info.value.status_code == 400
        assert "do not belong to the selected team" in exc_info.value.detail
        assert "Other Team Doc Set" in exc_info.value.detail


class TestToolTeamValidation:
    """Test validation of tools belonging to teams."""
    
    def test_validate_tools_empty_list(self):
        """Test that empty tool list passes validation."""
        db_session = Mock(spec=Session)
        
        # Should not raise any exception
        validate_tools_belong_to_team(
            tool_ids=[],
            user_teams=[1],
            db_session=db_session
        )
        
        # No database queries should be made
        db_session.query.assert_not_called()
    
    def test_validate_tools_public_persona_with_private_tools(self):
        """Test that public personas cannot use private tools."""
        db_session = Mock(spec=Session)
        
        # Mock private tool
        private_tool = Mock()
        private_tool.name = "Private Tool"
        private_tool.is_public = False
        
        db_session.query.return_value.filter.return_value.all.return_value = [private_tool]
        
        with pytest.raises(HTTPException) as exc_info:
            validate_tools_belong_to_team(
                tool_ids=[1],
                user_teams=[],  # Public persona (no teams)
                db_session=db_session
            )
        
        assert exc_info.value.status_code == 400
        assert "Public assistants cannot use private tools" in exc_info.value.detail
        assert "Private Tool" in exc_info.value.detail
    
    def test_validate_tools_public_persona_with_public_tools(self):
        """Test that public personas can use public tools."""
        db_session = Mock(spec=Session)
        
        # Mock that no private tools are found
        db_session.query.return_value.filter.return_value.all.return_value = []
        
        # Should not raise any exception
        validate_tools_belong_to_team(
            tool_ids=[1, 2],
            user_teams=[],  # Public persona (no teams)
            db_session=db_session
        )
    
    def test_validate_tools_private_persona_with_team_tools(self):
        """Test that private personas can use tools belonging to their team."""
        db_session = Mock(spec=Session)
        
        # Mock tool that belongs to the team
        tool = Mock()
        tool.id = 1
        tool.name = "Team Tool"
        tool.is_public = False
        
        # Mock team association
        team_association = Mock()
        
        # Setup query mocks
        query_mock = Mock()
        db_session.query.return_value = query_mock
        
        # First query for tools
        query_mock.filter.return_value.all.return_value = [tool]
        
        # Second query for team association
        query_mock.filter.return_value.first.return_value = team_association
        
        # Should not raise any exception
        validate_tools_belong_to_team(
            tool_ids=[1],
            user_teams=[1],
            db_session=db_session
        )
    
    def test_validate_tools_private_persona_without_team_access(self):
        """Test that private personas cannot use tools not belonging to their team."""
        db_session = Mock(spec=Session)
        
        # Mock tool that doesn't belong to the team
        tool = Mock()
        tool.id = 1
        tool.name = "Other Team Tool"
        tool.is_public = False
        
        # Setup query mocks
        query_mock = Mock()
        db_session.query.return_value = query_mock
        
        # First query for tools
        query_mock.filter.return_value.all.return_value = [tool]
        
        # Second query for team association (no association found)
        query_mock.filter.return_value.first.return_value = None
        
        with pytest.raises(HTTPException) as exc_info:
            validate_tools_belong_to_team(
                tool_ids=[1],
                user_teams=[1],
                db_session=db_session
            )
        
        assert exc_info.value.status_code == 400
        assert "do not belong to the selected team" in exc_info.value.detail
        assert "Other Team Tool" in exc_info.value.detail
    
    def test_validate_tools_mixed_public_and_team_tools(self):
        """Test validation with mix of public and team-specific tools."""
        db_session = Mock(spec=Session)
        
        # Mock public tool and team tool
        public_tool = Mock()
        public_tool.id = 1
        public_tool.name = "Public Tool"
        public_tool.is_public = True
        
        team_tool = Mock()
        team_tool.id = 2
        team_tool.name = "Team Tool"
        team_tool.is_public = False
        
        # Mock team association for team tool
        team_association = Mock()
        
        # Setup query mocks
        query_mock = Mock()
        db_session.query.return_value = query_mock
        
        # First query for tools
        query_mock.filter.return_value.all.return_value = [public_tool, team_tool]
        
        # Second query for team association (only for team tool)
        query_mock.filter.return_value.first.return_value = team_association
        
        # Should not raise any exception
        validate_tools_belong_to_team(
            tool_ids=[1, 2],
            user_teams=[1],
            db_session=db_session
        )
